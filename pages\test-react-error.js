/**
 * Test page to verify React Error #130 monitoring is working
 * This page intentionally triggers React Error #130 to test our error logging
 */

import { useState } from 'react';
import Layout from '../components/Layout';

export default function TestReactError() {
  const [showError, setShowError] = useState(false);

  // This will intentionally cause React Error #130
  const triggerError = () => {
    setShowError(true);
  };

  // This object will cause React Error #130 when rendered
  const problematicObject = {
    name: 'Test Object',
    value: 123,
    nested: { data: 'test' }
  };

  return (
    <Layout>
      <div style={{ padding: '2rem' }}>
        <h1>React Error #130 Test Page</h1>
        <p>This page is used to test our client-side error monitoring system.</p>
        
        <button 
          onClick={triggerError}
          style={{
            padding: '1rem 2rem',
            backgroundColor: '#ff4444',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginBottom: '2rem'
          }}
        >
          Trigger React Error #130
        </button>

        {showError && (
          <div>
            <h2>This will cause React Error #130:</h2>
            {/* This line will cause React Error #130 - rendering an object directly */}
            <p>Object content: {problematicObject}</p>
          </div>
        )}

        <div style={{ marginTop: '2rem', padding: '1rem', backgroundColor: '#f5f5f5' }}>
          <h3>Expected Behavior:</h3>
          <ul>
            <li>When you click the button, React Error #130 should occur</li>
            <li>The error should be caught by our client-side monitoring</li>
            <li>You should see a detailed error message in the server terminal</li>
            <li>The ErrorBoundary should display a fallback UI</li>
          </ul>
        </div>
      </div>
    </Layout>
  );
}
