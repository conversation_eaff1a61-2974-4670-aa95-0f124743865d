import Head from 'next/head'
import Link from 'next/link'
import React, { useState } from 'react'
import styles from '@/styles/Services.module.css'
import Layout from '@/components/Layout'
import ServicesHeroShowcase from '@/components/ServicesHeroShowcase'
import BookingModal from '@/components/BookingModal'

export default function Services() {
  const [selectedService, setSelectedService] = useState(null);
  const [showModal, setShowModal] = useState(false);

  const handleBookService = (service) => {
    // Convert the service data to the format expected by BookingModal
    const bookingService = {
      name: service.title,
      description: service.description,
      duration: '2 hours', // Default duration
      price: service.pricing[0].price,
      image: service.image,
      bookingType: 'Request to Book'
    };

    setSelectedService(bookingService);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
  };

  // Service data
  const services = [
    {
      id: 'airbrush',
      title: 'Airbrush Face & Body Painting',
      description: 'Add flair and colour to any event with our airbrush face and body painting. From intricate designs to bold statements, we tailor our artistry to suit your theme or outfit.',
      image: '/Airbrush Face Body Painting.png',
      category: 'painting',
      icon: '🎨',
      pricing: [
        { title: 'Individual designs', price: 'from $15' },
        { title: 'Group bookings (10+ people)', price: 'from $12 per person' },
        { title: 'Event packages', price: 'Please contact for custom quotes' }
      ],
      accentColor: '#4ECDC4'
    },
    {
      id: 'face-painting',
      title: 'Face Painting for Children',
      description: 'Bring magical moments to life with our kids\' face painting service, featuring enchanting designs like rainbows, unicorns, and superheroes. Ideal for children under 12.',
      image: '/images/services/face-paint.jpg',
      category: 'painting',
      icon: '🦄',
      pricing: [
        { title: 'Basic designs', price: 'from $10' },
        { title: 'Premium designs', price: 'from $15' },
        { title: 'Party packages (2 hours)', price: 'from $200' },
        { title: 'Additional hour', price: '$80' }
      ],
      accentColor: '#FFE66D'
    },
    {
      id: 'uv-painting',
      title: 'UV Face Paint & Body Art',
      description: 'Create a stunning glow-in-the-dark experience with our UV reactive face and body painting. Perfect for night events, parties, and festivals where you want to stand out in the dark.',
      image: '/UV-Generic-Psychadelia.jpg',
      category: 'painting',
      icon: '🌟',
      pricing: [
        { title: 'UV face designs', price: 'from $20' },
        { title: 'UV body art', price: 'from $40' },
        { title: 'Group bookings', price: 'from $15 per person' },
        { title: 'Event packages', price: 'Please contact for custom quotes' }
      ],
      accentColor: '#9C27B0'
    },
    {
      id: 'braiding',
      title: 'Hair Braids & Extensions',
      description: 'Transform your look with our vibrant braiding services, including festival-ready styles and coloured extensions to match your vibe. We offer single bookings, braid parties, and festival services.',
      image: '/images/services/festival-braids.jpg',
      category: 'hair',
      icon: '💇',
      pricing: [
        { title: 'Basic braids', price: 'from $60' },
        { title: 'Colored extensions', price: 'from $15 per color' },
        { title: 'Full head braiding', price: 'from $120' },
        { title: 'Braid parties (5+ people)', price: 'Please contact for group rates' }
      ],
      accentColor: '#FF6B6B'
    },
    {
      id: 'temp-tattoos',
      title: 'Temporary Tattoos',
      description: 'Get the tattoo experience without the commitment. Our temporary tattoos are perfect for events, parties, and festivals. Choose from our designs or request a custom creation.',
      image: '/Airbrush Temporary Tattoos.png',
      category: 'painting',
      icon: '🎭',
      pricing: [
        { title: 'Small designs', price: 'from $10' },
        { title: 'Medium designs', price: 'from $20' },
        { title: 'Large designs', price: 'from $30' },
        { title: 'Custom designs', price: 'from $25' }
      ],
      accentColor: '#607D8B'
    },
    {
      id: 'glitter',
      title: 'Biodegradable Glitter Art',
      description: 'Elevate your look with dazzling glitter designs using our eco-friendly biodegradable products. Perfect for festivals, parties, and events where you want to shine while caring for the environment.',
      image: '/Glitter Bar.png',
      category: 'sparkle',
      icon: '✨',
      pricing: [
        { title: 'Festival glitter', price: 'from $20' },
        { title: 'Face gems application', price: 'from $15' },
        { title: 'Full body sparkle', price: 'from $45' },
        { title: 'Glitter bar for events', price: 'from $300' }
      ],
      accentColor: '#1A73E8'
    }
  ];

  // Format services for the hero component
  const heroServices = services.map(service => ({
    title: service.title,
    icon: service.icon,
    color: service.accentColor,
    image: service.image
  }));

  // Format services for display

  return (
    <Layout>
      <Head>
        <title>Services | OceanSoulSparkles</title>
        <meta name="description" content="Explore our range of services including face painting, airbrush body art, and braiding for events, festivals, and parties." />
      </Head>

      <main className={styles.main}>
        {/* Hero Showcase with Floating Service Images */}
        <ServicesHeroShowcase
          title="Our Magical Services"
          subtitle="Transform your events with our creative and mesmerizing services"
          backgroundImage="/images/services-hero.jpg"
          services={heroServices}
          ctaText="Book Your Experience"
          ctaLink="/book-online"
        />

        {/* Service Cards Section */}
        <section className={styles.serviceCardsSection}>
          <div className={styles.sectionContainer}>
            <h2 className={styles.sectionTitle}>Our Services</h2>
            <p className={styles.sectionSubtitle}>
              Discover our range of creative services designed to make your event unforgettable
            </p>

            {/* Service cards */}
            <div className={styles.cardsGrid}>
              {services.map((service) => (
                <div
                  key={service.id}
                  className={styles.serviceCard}
                >
                  <div className={styles.cardContent}>
                    {/* Card front - Full image with title overlay */}
                    <div className={styles.cardFront}>
                      <div className={styles.imageContainer}>
                        <img
                          src={service.image}
                          alt={service.title}
                          className={styles.serviceImage}
                        />
                        <div
                          className={styles.imageOverlay}
                          style={{ backgroundColor: service.accentColor }}
                        ></div>
                        <div className={styles.serviceTitleOverlay}>
                          <h3 className={styles.serviceTitle}>{service.title}</h3>
                        </div>
                      </div>
                    </div>

                    {/* Card back - Service details and pricing */}
                    <div className={styles.cardExpanded}>
                      <h3 className={styles.pricingTitle}>{service.title}</h3>
                      <p className={styles.serviceDescription}>{service.description}</p>
                      <ul className={styles.pricingList}>
                        {service.pricing.map((item, idx) => (
                          <li key={idx} className={styles.pricingItem}>
                            <span className={styles.pricingItemTitle}>{item.title}</span>
                            <span className={styles.pricingItemPrice}>{item.price}</span>
                          </li>
                        ))}
                      </ul>
                      <button
                        onClick={() => handleBookService(service)}
                        className={styles.bookButton}
                      >
                        Book Now
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Gallery Link Section */}
        <section className={styles.galleryLinkSection}>
          <div className={styles.sectionContainer}>
            <h2 className={styles.sectionTitle}>Explore Our Work</h2>
            <p className={styles.sectionSubtitle}>
              Browse our gallery to see examples of our services and get inspired for your next event
            </p>
            <div className={styles.galleryLinkContainer}>
              <Link href="/gallery" className={styles.galleryLink}>
                View Gallery
              </Link>
            </div>
          </div>
        </section>

        {/* Booking Information Section */}
        <section className={styles.bookingInfo}>
          <h2 className={styles.sectionTitle}>Booking Information</h2>
          <p className={styles.sectionSubtitle}>
            Everything you need to know about booking our services for your next event
          </p>
          <div className={styles.infoGrid}>
            <div className={styles.infoCard}>
              <h3>Event Services</h3>
              <p>
                We offer special packages for events of all sizes, from intimate gatherings to large festivals.
                Our team can accommodate multiple services at once to ensure all your guests get the full experience.
              </p>
            </div>
            <div className={styles.infoCard}>
              <h3>Travel Information</h3>
              <p>
                We service the greater Melbourne area. Travel fees may apply for locations outside a 20km radius
                from Melbourne CBD. Please inquire for specific details when booking.
              </p>
            </div>
            <div className={styles.infoCard}>
              <h3>Booking Process</h3>
              <p>
                To secure your booking, we require a 50% deposit. The remaining balance is due on the day of the event.
                We accept various payment methods including bank transfer, PayPal, and credit card.
              </p>
            </div>
            <div className={styles.infoCard}>
              <h3>Cancellation Policy</h3>
              <p>
                Cancellations made more than 7 days before the event will receive a full refund of the deposit.
                Cancellations within 7 days of the event will forfeit the deposit. Rescheduling is available subject to availability.
              </p>
            </div>
          </div>
        </section>

        {/* Call to Action Section */}
        <section className={styles.cta}>
          <h2>Ready to add some sparkle to your event?</h2>
          <p>Contact us today to discuss your needs and get a custom quote.</p>
          <div className={styles.ctaButtons}>
            <Link href="/book-online" className={styles.button}>
              Book Online
            </Link>
            <Link href="/contact" className={`${styles.button} ${styles.outlineButton}`}>
              Contact Us
            </Link>
          </div>
        </section>
      </main>

      {/* Render the modal at the page level */}
      {showModal && selectedService && (
        <BookingModal
          service={selectedService}
          onClose={handleCloseModal}
          isRequestBooking={true}
        />
      )}
    </Layout>
  );
}
