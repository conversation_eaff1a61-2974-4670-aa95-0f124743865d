import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { getAuthToken } from '@/lib/auth-token-manager';
import { safeRender, safeFormatCurrency, safeFormatDuration } from '@/lib/safe-render-utils';
import styles from '@/styles/admin/ServiceList.module.css';

export default function ServiceList({ refreshKey = 0, onSelectService }) {
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [search, setSearch] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const [filters, setFilters] = useState({
    category: '',
    status: '',
  });

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search);
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  // Fetch services when search, sort, filters, or refreshKey changes
  const fetchServices = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams({
        sort_by: sortBy,
        sort_order: sortOrder,
      });

      if (debouncedSearch) {
        queryParams.append('search', debouncedSearch);
      }

      // Add filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          queryParams.append(key, value);
        }
      });

      // Get the current auth token
      const token = await getAuthToken();

      const response = await fetch(`/api/admin/services?${queryParams.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token || ''}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch services');
      }

      const data = await response.json();

      // Apply working pattern from /services page - convert all service data to safe primitive types
      const safeServices = (data.services || []).map(service => ({
        id: String(service.id || ''),
        name: String(service.name || ''),
        description: String(service.description || ''),
        price: String(service.price || ''),
        duration: String(service.duration || ''),
        category: String(service.category || ''),
        status: String(service.status || 'active'),
        image_url: String(service.image_url || ''),
        color: String(service.color || ''),
        featured: Boolean(service.featured),
        created_at: String(service.created_at || ''),
        updated_at: String(service.updated_at || '')
      }));

      setServices(safeServices);
    } catch (error) {
      console.error('Error fetching services:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }, [sortBy, sortOrder, debouncedSearch, filters]);

  useEffect(() => {
    fetchServices();
  }, [fetchServices, refreshKey]);

  // Handle sort column click
  const handleSort = (column) => {
    if (sortBy === column) {
      // Toggle sort order if clicking the same column
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // Default to ascending order for new column
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  // Render sort indicator
  const renderSortIndicator = (column) => {
    if (sortBy !== column) return null;

    return (
      <span className={styles.sortIndicator}>
        {sortOrder === 'asc' ? ' ↑' : ' ↓'}
      </span>
    );
  };

  // Handle filter change
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Use safe formatting functions
  const formatCurrency = safeFormatCurrency;
  const formatDuration = safeFormatDuration;

  return (
    <div className={styles.serviceListContainer}>
      <div className={styles.filters}>
        <div className={styles.searchBox}>
          <input
            type="text"
            placeholder="Search services..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className={styles.searchInput}
          />
        </div>

        <div className={styles.filterControls}>
          <div className={styles.filterItem}>
            <label>Category:</label>
            <select
              name="category"
              value={filters.category}
              onChange={handleFilterChange}
            >
              <option value="">All Categories</option>
              <option value="face-painting">Face Painting</option>
              <option value="body-art">Body Art</option>
              <option value="glitter">Glitter</option>
              <option value="braiding">Braiding</option>
              <option value="entertainment">Entertainment</option>
            </select>
          </div>

          <div className={styles.filterItem}>
            <label>Status:</label>
            <select
              name="status"
              value={filters.status}
              onChange={handleFilterChange}
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>

      {loading && (
        <div className={styles.loadingSpinner}>Loading services...</div>
      )}

      {error && (
        <div className={styles.errorMessage}>
          Error: {error}
          <button onClick={fetchServices}>Try Again</button>
        </div>
      )}

      {!loading && !error && services.length === 0 && (
        <div className={styles.noResults}>
          No services found. Try adjusting your search or filters.
        </div>
      )}

      {!loading && !error && services.length > 0 && (
        <div className={styles.tableContainer}>
          <table className={styles.serviceTable}>
            <thead>
              <tr>
                <th>Image</th>
                <th onClick={() => handleSort('name')}>
                  Service Name {renderSortIndicator('name')}
                </th>
                <th onClick={() => handleSort('price')}>
                  Price {renderSortIndicator('price')}
                </th>
                <th onClick={() => handleSort('duration')}>
                  Duration {renderSortIndicator('duration')}
                </th>
                <th onClick={() => handleSort('category')}>
                  Category {renderSortIndicator('category')}
                </th>
                <th onClick={() => handleSort('status')}>
                  Status {renderSortIndicator('status')}
                </th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {services.map((service) => {
                try {
                  return (
                    <tr key={service.id || Math.random()}>
                      <td className={styles.imageCell}>
                        {String(service.image_url || '') ? (
                          <Image
                            src={String(service.image_url || '')}
                            alt={String(service.name || 'Service')}
                            width={50}
                            height={50}
                            style={{ objectFit: 'cover' }}
                          />
                        ) : (
                          <div className={styles.noImage}>No Image</div>
                        )}
                      </td>
                      <td>{String(service.name || '')}</td>
                      <td>{String(service.price || '')}</td>
                      <td>{String(service.duration || '')}</td>
                      <td>{String(service.category || '')}</td>
                      <td>
                        <span
                          className={`${styles.statusBadge} ${
                            String(service.status || '') === 'active'
                              ? styles.statusActive
                              : styles.statusInactive
                          }`}
                        >
                          {String(service.status || '')}
                        </span>
                      </td>
                      <td className={styles.actions}>
                        <button
                          className={styles.editButton}
                          onClick={() => onSelectService(service)}
                        >
                          Edit
                        </button>
                      </td>
                    </tr>
                  );
                } catch (error) {
                  console.error('Error rendering service row:', error, 'Service:', service);
                  return (
                    <tr key={service.id || Math.random()}>
                      <td colSpan="6" style={{ color: 'red', padding: '10px' }}>
                        Error displaying service data. Please refresh the page.
                      </td>
                    </tr>
                  );
                }
              })}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
