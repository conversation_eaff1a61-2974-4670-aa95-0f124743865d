import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import styles from '@/styles/BookOnline.module.css'
import Layout from '@/components/Layout'
import Image from 'next/image'
import AnimatedSection from '@/components/AnimatedSection'
import BookingModal from '@/components/BookingModal'

// Updated bookingData
const bookingData = {
  pageTitle: "Book Your Services - OCEANSOULSPARKLES",
  pageDescription: "Book your face painting, airbrush body art, UV painting, hair braiding, and more with OCEANSOULSPARKLES. View our services and book online.",
  hero: {
    title: "Book Your Creative Experience",
    description: "Choose from our range of artistic services to add excitement to any occasion. From face painting and airbrush body art to hair braiding and glitter bars, we have something for everyone!",
    image: "/images/booking-hero.jpg" // Ensure this image exists and is suitable
  },
  intro: {
    title: "Our Creative Services",
    text: "At OCEANSOULSPARKLES, we bring creativity, colour, and sparkle to every event. From enchanting face painting and dazzling body art to vibrant festival braiding and eco-friendly glitter, our services make your moments magical."
  },
  servicesIntro: "Choose from our range of services:",
  services: [
    {
      id: 'airbrush-face-body',
      name: '🎨 AirBrush Face/Body Painting Activation',
      description: 'Professional airbrush face and body painting for events and activations. Perfect for festivals, corporate events, and special occasions.',
      duration: '2 hours',
      price: 'From A$350',
      image: '/images/services/airbrush-painting.jpeg',
      bookingLink: '#',
      bookingType: 'Book Now'
    },
    {
      id: 'kids-party',
      name: '🎉 Kids Party - Face Painting',
      description: 'Make your child\'s party unforgettable with our professional face painting service. We use only high-quality, skin-safe paints.',
      duration: '2 hours',
      price: 'From A$320',
      image: '/images/services/face-paint.jpg',
      bookingLink: '#',
      bookingType: 'Request to Book'
    },
    {
      id: 'uv-face-body',
      name: '✨ UV Face/Body Painting',
      description: 'Create a stunning glow-in-the-dark experience with our UV reactive face and body painting. Perfect for nighttime events and parties.',
      duration: '2 hours',
      price: 'From A$320',
      image: '/images/services/uv-face.jpg',
      bookingLink: '#',
      bookingType: 'Request to Book'
    },
    {
      id: 'glitter-bar',
      name: '✨ Glitter Bar',
      description: 'Add some sparkle to your event with our eco-friendly biodegradable glitter bar. Perfect for festivals, parties, and corporate events.',
      duration: '2 hours',
      price: 'From A$320',
      image: '/images/services/glitter-bar.jpg',
      bookingLink: '#',
      bookingType: 'Request to Book'
    },
    {
      id: 'braid-bar',
      name: '💇 Braid Bar',
      description: 'Our professional hair braiding service adds a touch of bohemian style to any event. Perfect for festivals, parties, and special occasions.',
      duration: '2 hours',
      price: 'From A$300',
      image: '/images/services/braid-bar.jpg',
      bookingLink: '#',
      bookingType: 'Book Now'
    },
    {
      id: 'airbrush-tattoos',
      name: '🎨 Airbrush Temporary Tattoos',
      description: 'High-quality, realistic temporary tattoos that last up to a week. Perfect for events, parties, and promotions.',
      duration: '2 hours',
      price: 'From A$350',
      image: '/images/services/airbrush-temp.jpg',
      bookingLink: '#',
      bookingType: 'Book Now'
    },
    {
      id: 'travel-fee',
      name: '🚗 Travel Fee',
      description: 'Travel fee for services outside our standard service area.',
      duration: '1 hour',
      price: 'A$25',
      image: '',
      bookingLink: '#',
      bookingType: 'Book Now'
    },
    {
      id: 'parking-fee',
      name: '🅿️ Parking Fee',
      description: 'Parking fee when required for event locations.',
      duration: '5 minutes',
      price: 'A$15',
      image: '',
      bookingLink: '#',
      bookingType: 'Book Now'
    },
    {
      id: 'body-art-photoshoot',
      name: '📸 Body Art Photo Shoot',
      description: 'Professional body art and photography session. Perfect for portfolio building, special occasions, or just for fun!',
      duration: '5 hours',
      price: 'From A$320',
      image: '/images/services/body-art-phot.jpg',
      bookingLink: '#',
      bookingType: 'Request to Book'
    },
    {
      id: 'facepaint-makeup',
      name: '🎭 Facepaint/Makeup Appointment',
      description: 'Individual appointment for face painting or creative makeup. Perfect for special occasions, Halloween, or just for fun!',
      duration: '1 hour',
      price: 'From A$60',
      image: '/images/services/facepaint-makup.jpg',
      bookingLink: '#',
      bookingType: 'Book Now'
    },
    {
      id: 'hair-braiding-short',
      name: '💇 Hair Braiding (under 1 hour)',
      description: 'Quick hair braiding service for individuals. Perfect for festivals, parties, or everyday style.',
      duration: '1 hour',
      price: 'From A$40',
      image: '/images/services/hair-braiding.jpg',
      bookingLink: '#',
      bookingType: 'Request to Book'
    },
    {
      id: 'viking-festival-braids',
      name: '💇 Viking - Festival Hair Braids',
      description: 'Elaborate Viking or festival-style hair braiding. Perfect for events, festivals, cosplay, or photoshoots.',
      duration: '1 hour',
      price: 'From A$130',
      image: '/images/services/festival-braids.jpg',
      bookingLink: '#',
      bookingType: 'Request to Book'
    },
    {
      id: 'barber-cut',
      name: '💇‍♂️ Barber Cut',
      description: 'Professional barber services for all hair types.',
      duration: '45 minutes',
      price: 'From A$20',
      image: '/images/services/barber-cut.jpg',
      bookingLink: '#',
      bookingType: 'Request to Book'
    },
    {
      id: 'hair-braiding',
      name: '💇 Hair Braiding',
      description: 'Professional hair braiding service for individuals. Various styles available.',
      duration: '45 minutes',
      price: 'From A$40',
      image: '/images/services/hair-braiding.jpg',
      bookingLink: '#',
      bookingType: 'Request to Book'
    },
  ],
  howToBook: {
    title: "How to Book Your Services",
    steps: [
      "Select your desired service from the options above.",
      "Click the 'Book Now' button for services that are immediately available.",
      "For services marked 'Request to Book', we'll review your request and confirm availability.",
      "Follow the prompts to provide details about your event or appointment.",
      "For most event bookings, a deposit is required to secure your date.",
      "You'll receive a confirmation email with all the details once your booking is confirmed."
    ]
  },
  paymentMethods: {
    title: "Payment Information",
    text: "We accept various payment methods including credit/debit cards and direct bank transfer. For event bookings such as parties, festivals, and corporate events, a deposit is required to secure your booking, with the remainder payable on the day of service. Individual appointments can be paid for at the time of service."
  },
  cancellationPolicy: {
    title: "Cancellation & Rescheduling",
    text: "We understand that plans can change. If you need to cancel or reschedule, please provide at least 48 hours notice for individual appointments and 7 days notice for events. Deposits for events are non-refundable but may be transferable to another date subject to availability. We appreciate your understanding."
  },
  contactInfo: {
    title: "Questions?",
    text: "If you have any questions about our services or booking, please don't hesitate to contact us!",
    email: "<EMAIL>",
    phone: "" // No phone number provided on the website
  }
};

function ServiceCard({ service, onBookService }) {
  const isRequestBooking = service.bookingType === 'Request to Book';

  const handleBookingClick = (e) => {
    e.preventDefault();
    if (service.isExternalLink) {
      window.open(service.bookingLink, '_blank', 'noopener,noreferrer');
    } else {
      onBookService(service);
    }
  };

  return (
    <div className={styles.serviceCard}>
      {service.image && (
        <div className={styles.serviceImageContainer}>
          <Image
            src={service.image}
            alt={service.name}
            width={400}
            height={250}
            className={styles.serviceImage}
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = '/images/services/placeholder.jpg'; // Fallback image
            }}
          />
        </div>
      )}
      <div className={styles.serviceCardContent}>
        <h3 className={styles.serviceName}>{service.name}</h3>
        {/* Ensure your CSS for serviceDescription handles line breaks, e.g., white-space: pre-line; */}
        <p className={styles.serviceDescription}>{service.description}</p>
        <div className={styles.serviceDetails}>
          {service.duration !== 'N/A' && <p><strong>Duration:</strong> {service.duration}</p>}
          <p><strong>Price:</strong> {service.price}</p>
        </div>
        <button
          onClick={handleBookingClick}
          className={`${styles.button} ${styles.buttonPrimary} ${isRequestBooking ? styles.buttonSecondary : ''}`}
        >
          {service.bookingType || 'Book Now'}
        </button>
      </div>
    </div>
  );
}

export default function BookOnlinePage() {
  const router = useRouter();
  const [selectedService, setSelectedService] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [preSelectedServiceId, setPreSelectedServiceId] = useState(null);

  // Use static services data to avoid React Error #130 issues
  // This is more reliable than dynamic API calls
  useEffect(() => {
    // Set static services data directly
    setServices(bookingData.services);
    setLoading(false);
  }, []);

  // Handle pre-selected service from URL parameter
  useEffect(() => {
    if (router.isReady && router.query.service) {
      setPreSelectedServiceId(router.query.service);

      // Find the service and auto-open booking modal
      const foundService = services.find(service => service.id === router.query.service);
      if (foundService) {
        // Convert database service to booking format
        const bookingService = {
          id: foundService.id,
          name: foundService.title || foundService.name,
          description: foundService.description,
          duration: foundService.duration ? `${foundService.duration} minutes` : '2 hours',
          price: foundService.pricing?.[0]?.price || `From $${foundService.price || '0'}`,
          image: foundService.image,
          bookingType: 'Request to Book'
        };

        setSelectedService(bookingService);
        setShowModal(true);
      }
    }
  }, [router.isReady, router.query.service, services]);

  const handleBookService = (service) => {
    setSelectedService(service);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    // Clear URL parameter when modal is closed
    if (router.query.service) {
      router.replace('/book-online', undefined, { shallow: true });
    }
  };

  return (
    <Layout>
      <Head>
        <title>{bookingData.pageTitle}</title>
        <meta name="description" content={bookingData.pageDescription} />
        <link rel="canonical" href="https://www.oceansoulsparkles.com.au/book-online" />
      </Head>
      <main className={styles.main}>
        <section className={styles.hero} style={{ backgroundImage: bookingData.hero.image ? `linear-gradient(rgba(222, 235, 255, 0.4), rgba(250, 235, 255, 0.4)), url(${bookingData.hero.image})` : 'none' }}>
          <div className={styles.heroContent}>
            <h1 className={styles.title}>{bookingData.hero.title}</h1>
            <p className={styles.description}>{bookingData.hero.description}</p>
          </div>
        </section>

        <section className={styles.introSection}>
          <h2>{bookingData.intro.title}</h2>
          <p>{bookingData.intro.text}</p>
        </section>

        <section className={styles.servicesSection}>
          <h2>{bookingData.servicesIntro}</h2>
          {loading ? (
            <div className={styles.loadingContainer}>
              <p>Loading services...</p>
            </div>
          ) : (
            <div className={styles.servicesGrid}>
              {services.map((service) => {
                // Convert database service to booking format
                const bookingService = {
                  id: service.id,
                  name: service.title || service.name,
                  description: service.description,
                  duration: service.duration ? `${service.duration} minutes` : '2 hours',
                  price: service.pricing?.[0]?.price || `From $${service.price || '0'}`,
                  image: service.image,
                  bookingType: 'Request to Book'
                };

                return (
                  <div
                    key={service.id}
                    className={`${styles.serviceCardWrapper} ${
                      preSelectedServiceId === service.id ? styles.highlighted : ''
                    }`}
                  >
                    <ServiceCard
                      service={bookingService}
                      onBookService={handleBookService}
                    />
                  </div>
                );
              })}
            </div>
          )}
        </section>

        <section className={styles.howToBookSection}>
          <div className={styles.infoCard}>
            <h3>{bookingData.howToBook.title}</h3>
            <ul>
              {bookingData.howToBook.steps.map((step, index) => (
                <li key={index}>{step}</li>
              ))}
            </ul>
          </div>
        </section>

        <section className={styles.additionalInfoSection}>
          <div className={styles.infoCard}>
            <h3>{bookingData.paymentMethods.title}</h3>
            <p>{bookingData.paymentMethods.text}</p>
          </div>
          <div className={styles.infoCard}>
            <h3>{bookingData.cancellationPolicy.title}</h3>
            <p>{bookingData.cancellationPolicy.text}</p>
          </div>
        </section>

        <section className={styles.policiesSection}>
          <div className={styles.infoCard}>
            <h3>Our Policies</h3>
            <p>Please review our policies before booking or making a purchase:</p>
            <div className={styles.policyLinks}>
              <Link href="/policies#shipping-info" className={styles.policyLink}>
                Shipping Information
              </Link>
              <Link href="/policies#return-policy" className={styles.policyLink}>
                Return & Refund Policy
              </Link>
            </div>
            <p className={styles.policyNote}>
              For event bookings, please note that we require a deposit to secure your date. This deposit is non-refundable but may be transferable to another date subject to availability.
              For product purchases such as UV Liner Palettes, we cannot accept returns on opened or used items due to hygiene concerns.
            </p>
          </div>
        </section>

        <section className={styles.contactSection}>
         <div className={styles.infoCard}>
          <h3>{bookingData.contactInfo.title}</h3>
          <p>{bookingData.contactInfo.text}</p>
          <p>Email: <a href={`mailto:${bookingData.contactInfo.email}`}>{bookingData.contactInfo.email}</a></p>
          {bookingData.contactInfo.phone && <p>Phone: {bookingData.contactInfo.phone}</p>}
          </div>
        </section>
      </main>

      {/* Render the modal at the page level */}
      {showModal && selectedService && (
        <BookingModal
          service={selectedService}
          onClose={handleCloseModal}
          isRequestBooking={selectedService.bookingType === 'Request to Book'}
        />
      )}
    </Layout>
  );
}
